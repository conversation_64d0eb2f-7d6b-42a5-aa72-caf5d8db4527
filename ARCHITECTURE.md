# PKCS#11 库架构设计文档

## 项目概述

本项目旨在实现一个全面的Golang PKCS#11库，提供从低级C API封装到高级易用接口的完整解决方案。

## 架构设计原则

1. **分层设计**: 采用清晰的分层架构，每层职责明确
2. **可扩展性**: 支持多版本PKCS#11标准和厂商扩展
3. **易用性**: 提供友好的高级API和标准接口实现
4. **性能**: 实现连接池和会话管理优化性能
5. **安全性**: 确保线程安全和错误处理

## 目录结构

```
gopkcs11/
├── README.md                    # 项目说明文档
├── LICENSE                      # 许可证文件
├── go.mod                       # Go模块定义
├── go.sum                       # Go依赖校验
├── ARCHITECTURE.md              # 架构设计文档
├── CHANGELOG.md                 # 变更日志
├── 
├── include/                     # PKCS#11头文件
│   ├── pkcs11-v24/             # PKCS#11 v2.40标准头文件
│   ├── pkcs11-v30/             # PKCS#11 v3.0标准头文件
│   └── utimaco/                # Utimaco厂商扩展头文件
│
├── pkg/                         # 核心库代码
│   ├── lowlevel/               # 低级封装层
│   │   ├── cgo.go              # CGO绑定
│   │   ├── types.go            # 类型定义
│   │   ├── functions.go        # 函数封装
│   │   ├── constants.go        # 常量定义
│   │   ├── errors.go           # 错误定义
│   │   └── version.go          # 版本管理
│   │
│   ├── middleware/             # 中间件层
│   │   ├── pool/               # 连接池管理
│   │   │   ├── pool.go
│   │   │   └── pool_test.go
│   │   ├── session/            # 会话管理
│   │   │   ├── manager.go
│   │   │   ├── session.go
│   │   │   └── session_test.go
│   │   ├── errors/             # 错误处理
│   │   │   ├── handler.go
│   │   │   ├── types.go
│   │   │   └── errors_test.go
│   │   ├── logging/            # 日志系统
│   │   │   ├── logger.go
│   │   │   ├── levels.go
│   │   │   └── logging_test.go
│   │   └── safety/             # 线程安全
│   │       ├── mutex.go
│   │       ├── rwlock.go
│   │       └── safety_test.go
│   │
│   ├── highlevel/              # 高级API层
│   │   ├── slot/               # Slot管理
│   │   │   ├── manager.go
│   │   │   ├── slot.go
│   │   │   └── slot_test.go
│   │   ├── keys/               # 密钥管理
│   │   │   ├── manager.go
│   │   │   ├── rsa.go
│   │   │   ├── ecdsa.go
│   │   │   ├── symmetric.go
│   │   │   └── keys_test.go
│   │   ├── crypto/             # 加密操作
│   │   │   ├── encrypt.go
│   │   │   ├── decrypt.go
│   │   │   ├── sign.go
│   │   │   ├── verify.go
│   │   │   └── crypto_test.go
│   │   └── certificates/       # 证书管理
│   │       ├── manager.go
│   │       ├── x509.go
│   │       └── cert_test.go
│   │
│   ├── interfaces/             # 标准接口实现
│   │   ├── signer.go           # crypto.Signer实现
│   │   ├── decrypter.go        # crypto.Decrypter实现
│   │   ├── rand.go             # 随机数生成器
│   │   └── interfaces_test.go
│   │
│   ├── extensions/             # 厂商扩展支持
│   │   ├── plugin/             # 插件管理器
│   │   │   ├── manager.go
│   │   │   ├── registry.go
│   │   │   └── plugin_test.go
│   │   ├── utimaco/            # Utimaco扩展
│   │   │   ├── extension.go
│   │   │   ├── mechanisms.go
│   │   │   ├── attributes.go
│   │   │   └── utimaco_test.go
│   │   └── vendor/             # 其他厂商扩展
│   │       └── template.go
│   │
│   └── config/                 # 配置管理
│       ├── config.go           # 配置结构
│       ├── loader.go           # 配置加载器
│       └── config_test.go
│
├── cmd/                        # 命令行工具
│   ├── pkcs11-tool/           # PKCS#11工具
│   │   └── main.go
│   └── examples/              # 示例程序
│       ├── basic/
│       ├── signing/
│       ├── encryption/
│       └── certificates/
│
├── test/                       # 测试相关
│   ├── integration/           # 集成测试
│   │   ├── softhsm_test.go
│   │   ├── cloudhsm_test.go
│   │   └── utimaco_test.go
│   ├── fixtures/              # 测试数据
│   │   ├── certificates/
│   │   ├── keys/
│   │   └── configs/
│   └── mocks/                 # 模拟对象
│       ├── hsm_mock.go
│       └── session_mock.go
│
├── docs/                       # 文档
│   ├── api/                   # API文档
│   ├── guides/                # 使用指南
│   │   ├── getting-started.md
│   │   ├── key-management.md
│   │   ├── signing.md
│   │   └── vendor-extensions.md
│   └── examples/              # 示例代码文档
│
├── scripts/                    # 构建和部署脚本
│   ├── build.sh
│   ├── test.sh
│   ├── generate.sh
│   └── docker/
│       ├── Dockerfile
│       └── docker-compose.yml
│
└── vendor/                     # 第三方依赖（可选）
```

## 核心组件说明

### 1. 低级封装层 (pkg/lowlevel/)
- **CGO绑定**: 处理Go与C代码的交互
- **类型定义**: 定义PKCS#11数据类型的Go映射
- **函数封装**: 封装所有PKCS#11 C函数
- **版本管理**: 支持多版本PKCS#11标准

### 2. 中间件层 (pkg/middleware/)
- **连接池**: 管理HSM连接，提高性能
- **会话管理**: 处理PKCS#11会话生命周期
- **错误处理**: 统一错误处理和转换
- **日志系统**: 提供结构化日志记录
- **线程安全**: 确保并发访问安全

### 3. 高级API层 (pkg/highlevel/)
- **Slot管理**: 简化slot操作和管理
- **密钥管理**: 提供密钥生成、导入、导出等操作
- **加密操作**: 封装加密、解密、签名、验证操作
- **证书管理**: 处理X.509证书相关操作

### 4. 标准接口层 (pkg/interfaces/)
- **crypto.Signer**: 实现Go标准签名接口
- **crypto.Decrypter**: 实现Go标准解密接口
- **随机数生成**: 提供HSM随机数生成

### 5. 厂商扩展层 (pkg/extensions/)
- **插件管理**: 动态加载和管理厂商扩展
- **Utimaco扩展**: 支持Utimaco HSM特定功能
- **扩展模板**: 为其他厂商提供扩展模板

## 设计特点

1. **模块化设计**: 每个组件都可以独立使用和测试
2. **接口驱动**: 使用接口定义组件间的交互
3. **配置驱动**: 支持灵活的配置管理
4. **测试友好**: 提供完整的测试覆盖和模拟对象
5. **文档完善**: 包含详细的API文档和使用指南

## 下一步实施计划

1. 实现低级封装层的CGO绑定和基础类型
2. 构建中间件层的核心基础设施
3. 开发高级API层的主要功能
4. 实现标准接口和厂商扩展支持
5. 完善测试框架和文档
