package lowlevel

/*
#cgo CFLAGS: -I../../include
#include <stdlib.h>

// Define required PKCS#11 macros
#define CK_PTR *
#define CK_DECLARE_FUNCTION(returnType, name) returnType name
#define CK_DECLARE_FUNCTION_POINTER(returnType, name) returnType (* name)
#define CK_CALLBACK_FUNCTION(returnType, name) returnType (* name)
#ifndef NULL_PTR
#define NULL_PTR 0
#endif

#ifdef PKCS11_V30
    #include "pkcs11-v30/pkcs11.h"
#else
    #include "pkcs11-v24/pkcs11.h"
#endif

// Forward declarations for wrapper functions
extern CK_RV go_C_Initialize(CK_VOID_PTR pInitArgs);
extern CK_RV go_C_Finalize(CK_VOID_PTR pReserved);
extern CK_RV go_C_GetInfo(CK_INFO_PTR pInfo);
extern CK_RV go_C_GetSlotList(CK_<PERSON><PERSON><PERSON> tokenPresent, CK_SLOT_ID_PTR pSlotList, CK_ULONG_PTR pulCount);
extern CK_RV go_C_GetSlotInfo(CK_SLOT_ID slotID, CK_SLOT_INFO_PTR pInfo);
extern CK_RV go_C_GetTokenInfo(CK_SLOT_ID slotID, CK_TOKEN_INFO_PTR pInfo);
extern CK_RV go_C_OpenSession(CK_SLOT_ID slotID, CK_FLAGS flags, CK_VOID_PTR pApplication, CK_NOTIFY Notify, CK_SESSION_HANDLE_PTR phSession);
extern CK_RV go_C_CloseSession(CK_SESSION_HANDLE hSession);
extern CK_RV go_C_Login(CK_SESSION_HANDLE hSession, CK_USER_TYPE userType, CK_UTF8CHAR_PTR pPin, CK_ULONG ulPinLen);
extern CK_RV go_C_Logout(CK_SESSION_HANDLE hSession);
extern CK_RV go_C_GenerateKeyPair(CK_SESSION_HANDLE hSession, CK_MECHANISM_PTR pMechanism, CK_ATTRIBUTE_PTR pPublicKeyTemplate, CK_ULONG ulPublicKeyAttributeCount, CK_ATTRIBUTE_PTR pPrivateKeyTemplate, CK_ULONG ulPrivateKeyAttributeCount, CK_OBJECT_HANDLE_PTR phPublicKey, CK_OBJECT_HANDLE_PTR phPrivateKey);
extern CK_RV go_C_GenerateKey(CK_SESSION_HANDLE hSession, CK_MECHANISM_PTR pMechanism, CK_ATTRIBUTE_PTR pTemplate, CK_ULONG ulCount, CK_OBJECT_HANDLE_PTR phKey);
extern CK_RV go_C_EncryptInit(CK_SESSION_HANDLE hSession, CK_MECHANISM_PTR pMechanism, CK_OBJECT_HANDLE hKey);
extern CK_RV go_C_Encrypt(CK_SESSION_HANDLE hSession, CK_BYTE_PTR pData, CK_ULONG ulDataLen, CK_BYTE_PTR pEncryptedData, CK_ULONG_PTR pulEncryptedDataLen);
extern CK_RV go_C_DecryptInit(CK_SESSION_HANDLE hSession, CK_MECHANISM_PTR pMechanism, CK_OBJECT_HANDLE hKey);
extern CK_RV go_C_Decrypt(CK_SESSION_HANDLE hSession, CK_BYTE_PTR pEncryptedData, CK_ULONG ulEncryptedDataLen, CK_BYTE_PTR pData, CK_ULONG_PTR pulDataLen);
extern CK_RV go_C_SignInit(CK_SESSION_HANDLE hSession, CK_MECHANISM_PTR pMechanism, CK_OBJECT_HANDLE hKey);
extern CK_RV go_C_Sign(CK_SESSION_HANDLE hSession, CK_BYTE_PTR pData, CK_ULONG ulDataLen, CK_BYTE_PTR pSignature, CK_ULONG_PTR pulSignatureLen);
extern CK_RV go_C_VerifyInit(CK_SESSION_HANDLE hSession, CK_MECHANISM_PTR pMechanism, CK_OBJECT_HANDLE hKey);
extern CK_RV go_C_Verify(CK_SESSION_HANDLE hSession, CK_BYTE_PTR pData, CK_ULONG ulDataLen, CK_BYTE_PTR pSignature, CK_ULONG ulSignatureLen);
extern CK_RV go_C_GenerateRandom(CK_SESSION_HANDLE hSession, CK_BYTE_PTR RandomData, CK_ULONG ulRandomLen);
extern CK_RV go_C_FindObjectsInit(CK_SESSION_HANDLE hSession, CK_ATTRIBUTE_PTR pTemplate, CK_ULONG ulCount);
extern CK_RV go_C_FindObjects(CK_SESSION_HANDLE hSession, CK_OBJECT_HANDLE_PTR phObject, CK_ULONG ulMaxObjectCount, CK_ULONG_PTR pulObjectCount);
extern CK_RV go_C_FindObjectsFinal(CK_SESSION_HANDLE hSession);
extern CK_RV go_C_GetAttributeValue(CK_SESSION_HANDLE hSession, CK_OBJECT_HANDLE hObject, CK_ATTRIBUTE_PTR pTemplate, CK_ULONG ulCount);
extern CK_RV go_C_SetAttributeValue(CK_SESSION_HANDLE hSession, CK_OBJECT_HANDLE hObject, CK_ATTRIBUTE_PTR pTemplate, CK_ULONG ulCount);
extern CK_RV go_C_CreateObject(CK_SESSION_HANDLE hSession, CK_ATTRIBUTE_PTR pTemplate, CK_ULONG ulCount, CK_OBJECT_HANDLE_PTR phObject);
extern CK_RV go_C_DestroyObject(CK_SESSION_HANDLE hSession, CK_OBJECT_HANDLE hObject);
*/
import "C"
import (
	"unsafe"
)

// Initialize initializes the PKCS#11 library
func Initialize() error {
	rv := C.go_C_Initialize(nil)
	return CheckResult(CKRV(rv), "C_Initialize")
}

// Finalize finalizes the PKCS#11 library
func Finalize() error {
	rv := C.go_C_Finalize(nil)
	return CheckResult(CKRV(rv), "C_Finalize")
}

// GetInfo gets general information about the PKCS#11 library
func GetInfo() (*CKInfo, error) {
	var cInfo C.CK_INFO
	rv := C.go_C_GetInfo(&cInfo)
	if err := CheckResult(CKRV(rv), "C_GetInfo"); err != nil {
		return nil, err
	}

	info := &CKInfo{}
	info.FromCInfo(cInfo)
	return info, nil
}

// GetSlotList gets the list of available slots
func GetSlotList(tokenPresent bool) ([]CKSlotID, error) {
	var count C.CK_ULONG
	var present C.CK_BBOOL = C.CK_FALSE
	if tokenPresent {
		present = C.CK_TRUE
	}

	// First call to get the count
	rv := C.go_C_GetSlotList(present, nil, &count)
	if err := CheckResult(CKRV(rv), "C_GetSlotList"); err != nil {
		return nil, err
	}

	if count == 0 {
		return []CKSlotID{}, nil
	}

	// Second call to get the actual slots
	cSlots := make([]C.CK_SLOT_ID, count)
	rv = C.go_C_GetSlotList(present, &cSlots[0], &count)
	if err := CheckResult(CKRV(rv), "C_GetSlotList"); err != nil {
		return nil, err
	}

	slots := make([]CKSlotID, count)
	for i := range slots {
		slots[i] = CKSlotID(cSlots[i])
	}

	return slots, nil
}

// GetSlotInfo gets information about a specific slot
func GetSlotInfo(slotID CKSlotID) (*CKSlotInfo, error) {
	var cInfo C.CK_SLOT_INFO
	rv := C.go_C_GetSlotInfo(C.CK_SLOT_ID(slotID), &cInfo)
	if err := CheckResult(CKRV(rv), "C_GetSlotInfo"); err != nil {
		return nil, err
	}

	info := &CKSlotInfo{}
	info.FromCSlotInfo(cInfo)
	return info, nil
}

// GetTokenInfo gets information about a token in a specific slot
func GetTokenInfo(slotID CKSlotID) (*CKTokenInfo, error) {
	var cInfo C.CK_TOKEN_INFO
	rv := C.go_C_GetTokenInfo(C.CK_SLOT_ID(slotID), &cInfo)
	if err := CheckResult(CKRV(rv), "C_GetTokenInfo"); err != nil {
		return nil, err
	}

	info := &CKTokenInfo{}
	// Convert C struct to Go struct (implementation needed)
	// This is a placeholder - full implementation would convert all fields
	return info, nil
}

// OpenSession opens a session with a token
func OpenSession(slotID CKSlotID, flags CKFlags) (CKSessionHandle, error) {
	var session C.CK_SESSION_HANDLE
	rv := C.go_C_OpenSession(C.CK_SLOT_ID(slotID), C.CK_FLAGS(flags), nil, nil, &session)
	if err := CheckResult(CKRV(rv), "C_OpenSession"); err != nil {
		return 0, err
	}

	return CKSessionHandle(session), nil
}

// CloseSession closes a session
func CloseSession(session CKSessionHandle) error {
	rv := C.go_C_CloseSession(C.CK_SESSION_HANDLE(session))
	return CheckResult(CKRV(rv), "C_CloseSession")
}

// Login logs into a token
func Login(session CKSessionHandle, userType CKUserType, pin string) error {
	var cPin *C.CK_UTF8CHAR
	var pinLen C.CK_ULONG

	if pin != "" {
		cPinStr := C.CString(pin)
		defer C.free(unsafe.Pointer(cPinStr))
		cPin = (*C.CK_UTF8CHAR)(unsafe.Pointer(cPinStr))
		pinLen = C.CK_ULONG(len(pin))
	}

	rv := C.go_C_Login(C.CK_SESSION_HANDLE(session), C.CK_USER_TYPE(userType), cPin, pinLen)
	return CheckResult(CKRV(rv), "C_Login")
}

// Logout logs out from a token
func Logout(session CKSessionHandle) error {
	rv := C.go_C_Logout(C.CK_SESSION_HANDLE(session))
	return CheckResult(CKRV(rv), "C_Logout")
}

// GenerateKeyPair generates a public/private key pair
func GenerateKeyPair(session CKSessionHandle, mechanism *CKMechanism,
	publicTemplate, privateTemplate []CKAttribute) (CKObjectHandle, CKObjectHandle, error) {

	cMech := mechanism.ToCMechanism()

	var cPubTemplate *C.CK_ATTRIBUTE
	var pubCount C.CK_ULONG
	if len(publicTemplate) > 0 {
		cPubTemplate, pubCount = AttributesToCAttributes(publicTemplate)
	}

	var cPrivTemplate *C.CK_ATTRIBUTE
	var privCount C.CK_ULONG
	if len(privateTemplate) > 0 {
		cPrivTemplate, privCount = AttributesToCAttributes(privateTemplate)
	}

	var pubKey, privKey C.CK_OBJECT_HANDLE
	rv := C.go_C_GenerateKeyPair(C.CK_SESSION_HANDLE(session), &cMech,
		cPubTemplate, pubCount, cPrivTemplate, privCount, &pubKey, &privKey)

	if err := CheckResult(CKRV(rv), "C_GenerateKeyPair"); err != nil {
		return 0, 0, err
	}

	return CKObjectHandle(pubKey), CKObjectHandle(privKey), nil
}

// GenerateKey generates a secret key
func GenerateKey(session CKSessionHandle, mechanism *CKMechanism, template []CKAttribute) (CKObjectHandle, error) {
	cMech := mechanism.ToCMechanism()

	var cTemplate *C.CK_ATTRIBUTE
	var count C.CK_ULONG
	if len(template) > 0 {
		cTemplate, count = AttributesToCAttributes(template)
	}

	var key C.CK_OBJECT_HANDLE
	rv := C.go_C_GenerateKey(C.CK_SESSION_HANDLE(session), &cMech, cTemplate, count, &key)

	if err := CheckResult(CKRV(rv), "C_GenerateKey"); err != nil {
		return 0, err
	}

	return CKObjectHandle(key), nil
}

// EncryptInit initializes an encryption operation
func EncryptInit(session CKSessionHandle, mechanism *CKMechanism, key CKObjectHandle) error {
	cMech := mechanism.ToCMechanism()
	rv := C.go_C_EncryptInit(C.CK_SESSION_HANDLE(session), &cMech, C.CK_OBJECT_HANDLE(key))
	return CheckResult(CKRV(rv), "C_EncryptInit")
}

// Encrypt encrypts data
func Encrypt(session CKSessionHandle, data []byte) ([]byte, error) {
	var cData *C.CK_BYTE
	var dataLen C.CK_ULONG

	if len(data) > 0 {
		cData, dataLen = BytesToCBytes(data)
	}

	// First call to get the required buffer size
	var encryptedLen C.CK_ULONG
	rv := C.go_C_Encrypt(C.CK_SESSION_HANDLE(session), cData, dataLen, nil, &encryptedLen)
	if err := CheckResult(CKRV(rv), "C_Encrypt"); err != nil {
		return nil, err
	}

	// Second call to get the actual encrypted data
	encrypted := make([]byte, encryptedLen)
	var cEncrypted *C.CK_BYTE
	if encryptedLen > 0 {
		cEncrypted = (*C.CK_BYTE)(unsafe.Pointer(&encrypted[0]))
	}

	rv = C.go_C_Encrypt(C.CK_SESSION_HANDLE(session), cData, dataLen, cEncrypted, &encryptedLen)
	if err := CheckResult(CKRV(rv), "C_Encrypt"); err != nil {
		return nil, err
	}

	return encrypted[:encryptedLen], nil
}

// DecryptInit initializes a decryption operation
func DecryptInit(session CKSessionHandle, mechanism *CKMechanism, key CKObjectHandle) error {
	cMech := mechanism.ToCMechanism()
	rv := C.go_C_DecryptInit(C.CK_SESSION_HANDLE(session), &cMech, C.CK_OBJECT_HANDLE(key))
	return CheckResult(CKRV(rv), "C_DecryptInit")
}

// Decrypt decrypts data
func Decrypt(session CKSessionHandle, encryptedData []byte) ([]byte, error) {
	var cEncrypted *C.CK_BYTE
	var encryptedLen C.CK_ULONG

	if len(encryptedData) > 0 {
		cEncrypted, encryptedLen = BytesToCBytes(encryptedData)
	}

	// First call to get the required buffer size
	var dataLen C.CK_ULONG
	rv := C.go_C_Decrypt(C.CK_SESSION_HANDLE(session), cEncrypted, encryptedLen, nil, &dataLen)
	if err := CheckResult(CKRV(rv), "C_Decrypt"); err != nil {
		return nil, err
	}

	// Second call to get the actual decrypted data
	data := make([]byte, dataLen)
	var cData *C.CK_BYTE
	if dataLen > 0 {
		cData = (*C.CK_BYTE)(unsafe.Pointer(&data[0]))
	}

	rv = C.go_C_Decrypt(C.CK_SESSION_HANDLE(session), cEncrypted, encryptedLen, cData, &dataLen)
	if err := CheckResult(CKRV(rv), "C_Decrypt"); err != nil {
		return nil, err
	}

	return data[:dataLen], nil
}

// SignInit initializes a signing operation
func SignInit(session CKSessionHandle, mechanism *CKMechanism, key CKObjectHandle) error {
	cMech := mechanism.ToCMechanism()
	rv := C.go_C_SignInit(C.CK_SESSION_HANDLE(session), &cMech, C.CK_OBJECT_HANDLE(key))
	return CheckResult(CKRV(rv), "C_SignInit")
}

// Sign signs data
func Sign(session CKSessionHandle, data []byte) ([]byte, error) {
	var cData *C.CK_BYTE
	var dataLen C.CK_ULONG

	if len(data) > 0 {
		cData, dataLen = BytesToCBytes(data)
	}

	// First call to get the required buffer size
	var signatureLen C.CK_ULONG
	rv := C.go_C_Sign(C.CK_SESSION_HANDLE(session), cData, dataLen, nil, &signatureLen)
	if err := CheckResult(CKRV(rv), "C_Sign"); err != nil {
		return nil, err
	}

	// Second call to get the actual signature
	signature := make([]byte, signatureLen)
	var cSignature *C.CK_BYTE
	if signatureLen > 0 {
		cSignature = (*C.CK_BYTE)(unsafe.Pointer(&signature[0]))
	}

	rv = C.go_C_Sign(C.CK_SESSION_HANDLE(session), cData, dataLen, cSignature, &signatureLen)
	if err := CheckResult(CKRV(rv), "C_Sign"); err != nil {
		return nil, err
	}

	return signature[:signatureLen], nil
}

// VerifyInit initializes a verification operation
func VerifyInit(session CKSessionHandle, mechanism *CKMechanism, key CKObjectHandle) error {
	cMech := mechanism.ToCMechanism()
	rv := C.go_C_VerifyInit(C.CK_SESSION_HANDLE(session), &cMech, C.CK_OBJECT_HANDLE(key))
	return CheckResult(CKRV(rv), "C_VerifyInit")
}

// Verify verifies a signature
func Verify(session CKSessionHandle, data, signature []byte) error {
	var cData *C.CK_BYTE
	var dataLen C.CK_ULONG
	if len(data) > 0 {
		cData, dataLen = BytesToCBytes(data)
	}

	var cSignature *C.CK_BYTE
	var signatureLen C.CK_ULONG
	if len(signature) > 0 {
		cSignature, signatureLen = BytesToCBytes(signature)
	}

	rv := C.go_C_Verify(C.CK_SESSION_HANDLE(session), cData, dataLen, cSignature, signatureLen)
	return CheckResult(CKRV(rv), "C_Verify")
}

// GenerateRandom generates random data
func GenerateRandom(session CKSessionHandle, length int) ([]byte, error) {
	if length <= 0 {
		return []byte{}, nil
	}

	randomData := make([]byte, length)
	cData := (*C.CK_BYTE)(unsafe.Pointer(&randomData[0]))

	rv := C.go_C_GenerateRandom(C.CK_SESSION_HANDLE(session), cData, C.CK_ULONG(length))
	if err := CheckResult(CKRV(rv), "C_GenerateRandom"); err != nil {
		return nil, err
	}

	return randomData, nil
}

// FindObjectsInit initializes an object search operation
func FindObjectsInit(session CKSessionHandle, template []CKAttribute) error {
	var cTemplate *C.CK_ATTRIBUTE
	var count C.CK_ULONG

	if len(template) > 0 {
		cTemplate, count = AttributesToCAttributes(template)
	}

	rv := C.go_C_FindObjectsInit(C.CK_SESSION_HANDLE(session), cTemplate, count)
	return CheckResult(CKRV(rv), "C_FindObjectsInit")
}

// FindObjects continues an object search operation
func FindObjects(session CKSessionHandle, maxObjects int) ([]CKObjectHandle, error) {
	if maxObjects <= 0 {
		return []CKObjectHandle{}, nil
	}

	cObjects := make([]C.CK_OBJECT_HANDLE, maxObjects)
	var count C.CK_ULONG

	rv := C.go_C_FindObjects(C.CK_SESSION_HANDLE(session), &cObjects[0],
		C.CK_ULONG(maxObjects), &count)
	if err := CheckResult(CKRV(rv), "C_FindObjects"); err != nil {
		return nil, err
	}

	objects := make([]CKObjectHandle, count)
	for i := range objects {
		objects[i] = CKObjectHandle(cObjects[i])
	}

	return objects, nil
}

// FindObjectsFinal finalizes an object search operation
func FindObjectsFinal(session CKSessionHandle) error {
	rv := C.go_C_FindObjectsFinal(C.CK_SESSION_HANDLE(session))
	return CheckResult(CKRV(rv), "C_FindObjectsFinal")
}

// GetAttributeValue gets attribute values from an object
func GetAttributeValue(session CKSessionHandle, object CKObjectHandle, template []CKAttribute) ([]CKAttribute, error) {
	if len(template) == 0 {
		return []CKAttribute{}, nil
	}

	cTemplate, count := AttributesToCAttributes(template)

	// First call to get the required buffer sizes
	rv := C.go_C_GetAttributeValue(C.CK_SESSION_HANDLE(session),
		C.CK_OBJECT_HANDLE(object), cTemplate, count)
	if err := CheckResult(CKRV(rv), "C_GetAttributeValue"); err != nil {
		return nil, err
	}

	// Allocate buffers for the attribute values
	attrs := CAttributesToAttributes(cTemplate, count)
	for i := range attrs {
		if attrs[i].ValueLen > 0 {
			buffer := make([]byte, attrs[i].ValueLen)
			attrs[i].Value = unsafe.Pointer(&buffer[0])
		}
	}

	// Update C template with allocated buffers
	cTemplate, count = AttributesToCAttributes(attrs)

	// Second call to get the actual attribute values
	rv = C.go_C_GetAttributeValue(C.CK_SESSION_HANDLE(session),
		C.CK_OBJECT_HANDLE(object), cTemplate, count)
	if err := CheckResult(CKRV(rv), "C_GetAttributeValue"); err != nil {
		return nil, err
	}

	return CAttributesToAttributes(cTemplate, count), nil
}

// SetAttributeValue sets attribute values on an object
func SetAttributeValue(session CKSessionHandle, object CKObjectHandle, template []CKAttribute) error {
	if len(template) == 0 {
		return nil
	}

	cTemplate, count := AttributesToCAttributes(template)

	rv := C.go_C_SetAttributeValue(C.CK_SESSION_HANDLE(session),
		C.CK_OBJECT_HANDLE(object), cTemplate, count)
	return CheckResult(CKRV(rv), "C_SetAttributeValue")
}

// CreateObject creates a new object
func CreateObject(session CKSessionHandle, template []CKAttribute) (CKObjectHandle, error) {
	var cTemplate *C.CK_ATTRIBUTE
	var count C.CK_ULONG

	if len(template) > 0 {
		cTemplate, count = AttributesToCAttributes(template)
	}

	var object C.CK_OBJECT_HANDLE
	rv := C.go_C_CreateObject(C.CK_SESSION_HANDLE(session), cTemplate, count, &object)
	if err := CheckResult(CKRV(rv), "C_CreateObject"); err != nil {
		return 0, err
	}

	return CKObjectHandle(object), nil
}

// DestroyObject destroys an object
func DestroyObject(session CKSessionHandle, object CKObjectHandle) error {
	rv := C.go_C_DestroyObject(C.CK_SESSION_HANDLE(session), C.CK_OBJECT_HANDLE(object))
	return CheckResult(CKRV(rv), "C_DestroyObject")
}
