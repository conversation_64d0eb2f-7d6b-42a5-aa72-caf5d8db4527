package lowlevel

import (
	"fmt"
	"strconv"
	"strings"
)

// Version represents a version number with major, minor, and patch components
type Version struct {
	Major int
	Minor int
	Patch int
}

// String returns the string representation of the version
func (v Version) String() string {
	return fmt.Sprintf("%d.%d.%d", v.Major, v.Minor, v.<PERSON>)
}

// Compare compares two versions
// Returns: -1 if v < other, 0 if v == other, 1 if v > other
func (v Version) Compare(other Version) int {
	if v.Major != other.Major {
		if v.Major < other.Major {
			return -1
		}
		return 1
	}
	
	if v.Minor != other.Minor {
		if v.Minor < other.Minor {
			return -1
		}
		return 1
	}
	
	if v.Patch != other.Patch {
		if v.Patch < other.Patch {
			return -1
		}
		return 1
	}
	
	return 0
}

// IsCompatible checks if this version is compatible with the required version
func (v Version) IsCompatible(required Version) bool {
	// Major version must match
	if v.Major != required.Major {
		return false
	}
	
	// Minor version must be >= required
	if v.Minor < required.Minor {
		return false
	}
	
	// If minor versions match, patch must be >= required
	if v.Minor == required.Minor && v.Patch < required.Patch {
		return false
	}
	
	return true
}

// ParseVersion parses a version string in the format "major.minor.patch"
func ParseVersion(s string) (Version, error) {
	parts := strings.Split(s, ".")
	if len(parts) != 3 {
		return Version{}, fmt.Errorf("invalid version format: %s", s)
	}
	
	major, err := strconv.Atoi(parts[0])
	if err != nil {
		return Version{}, fmt.Errorf("invalid major version: %s", parts[0])
	}
	
	minor, err := strconv.Atoi(parts[1])
	if err != nil {
		return Version{}, fmt.Errorf("invalid minor version: %s", parts[1])
	}
	
	patch, err := strconv.Atoi(parts[2])
	if err != nil {
		return Version{}, fmt.Errorf("invalid patch version: %s", parts[2])
	}
	
	return Version{Major: major, Minor: minor, Patch: patch}, nil
}

// PKCS#11 version constants
var (
	// PKCS11Version240 represents PKCS#11 v2.40
	PKCS11Version240 = Version{Major: 2, Minor: 40, Patch: 0}
	
	// PKCS11Version300 represents PKCS#11 v3.0
	PKCS11Version300 = Version{Major: 3, Minor: 0, Patch: 0}
	
	// SupportedVersions lists all supported PKCS#11 versions
	SupportedVersions = []Version{
		PKCS11Version240,
		PKCS11Version300,
	}
)

// PKCS11VersionInfo represents PKCS#11 version information
type PKCS11VersionInfo struct {
	CryptokiVersion Version
	LibraryVersion  Version
	Manufacturer    string
	Description     string
}

// String returns a string representation of the version info
func (vi PKCS11VersionInfo) String() string {
	return fmt.Sprintf("PKCS#11 %s, Library %s (%s - %s)",
		vi.CryptokiVersion.String(),
		vi.LibraryVersion.String(),
		vi.Manufacturer,
		vi.Description)
}

// GetCryptokiVersion returns the Cryptoki version from CKVersion
func GetCryptokiVersion(ckVersion CKVersion) Version {
	return Version{
		Major: int(ckVersion.Major),
		Minor: int(ckVersion.Minor),
		Patch: 0, // PKCS#11 CK_VERSION doesn't have patch
	}
}

// ToCKVersion converts a Version to CKVersion
func (v Version) ToCKVersion() CKVersion {
	return CKVersion{
		Major: CKByte(v.Major),
		Minor: CKByte(v.Minor),
	}
}

// IsSupportedVersion checks if the given version is supported
func IsSupportedVersion(version Version) bool {
	for _, supported := range SupportedVersions {
		if version.Compare(supported) == 0 {
			return true
		}
	}
	return false
}

// GetMinimumSupportedVersion returns the minimum supported PKCS#11 version
func GetMinimumSupportedVersion() Version {
	return PKCS11Version240
}

// GetMaximumSupportedVersion returns the maximum supported PKCS#11 version
func GetMaximumSupportedVersion() Version {
	return PKCS11Version300
}

// IsVersionInRange checks if the version is within the supported range
func IsVersionInRange(version Version) bool {
	min := GetMinimumSupportedVersion()
	max := GetMaximumSupportedVersion()
	
	return version.Compare(min) >= 0 && version.Compare(max) <= 0
}

// GetVersionFeatures returns the features available in the given version
func GetVersionFeatures(version Version) []string {
	features := []string{
		"Basic PKCS#11 operations",
		"RSA key generation and operations",
		"ECDSA key generation and operations",
		"AES encryption/decryption",
		"SHA-1/SHA-2 hashing",
		"Random number generation",
	}
	
	if version.Compare(PKCS11Version300) >= 0 {
		features = append(features, []string{
			"Enhanced attribute handling",
			"Improved mechanism support",
			"Extended error codes",
			"Better vendor extension support",
		}...)
	}
	
	return features
}

// VersionCompatibilityMatrix defines compatibility between library and PKCS#11 versions
type VersionCompatibilityMatrix struct {
	LibraryVersion Version
	PKCS11Versions []Version
}

// DefaultCompatibilityMatrix defines the default compatibility matrix
var DefaultCompatibilityMatrix = []VersionCompatibilityMatrix{
	{
		LibraryVersion: Version{Major: 1, Minor: 0, Patch: 0},
		PKCS11Versions: []Version{PKCS11Version240, PKCS11Version300},
	},
}

// IsLibraryCompatible checks if the library version is compatible with PKCS#11 version
func IsLibraryCompatible(libraryVersion, pkcs11Version Version) bool {
	for _, matrix := range DefaultCompatibilityMatrix {
		if matrix.LibraryVersion.Compare(libraryVersion) == 0 {
			for _, supported := range matrix.PKCS11Versions {
				if supported.Compare(pkcs11Version) == 0 {
					return true
				}
			}
		}
	}
	return false
}

// GetRecommendedVersion returns the recommended PKCS#11 version for new implementations
func GetRecommendedVersion() Version {
	return PKCS11Version300
}

// Library version information
const (
	LibraryName        = "gopkcs11"
	LibraryDescription = "Comprehensive PKCS#11 library for Go"
	LibraryMajor       = 1
	LibraryMinor       = 0
	LibraryPatch       = 0
)

// GetLibraryVersion returns the current library version
func GetLibraryVersion() Version {
	return Version{
		Major: LibraryMajor,
		Minor: LibraryMinor,
		Patch: LibraryPatch,
	}
}

// GetLibraryInfo returns library information
func GetLibraryInfo() PKCS11VersionInfo {
	return PKCS11VersionInfo{
		CryptokiVersion: GetRecommendedVersion(),
		LibraryVersion:  GetLibraryVersion(),
		Manufacturer:    "gopkcs11 project",
		Description:     LibraryDescription,
	}
}
