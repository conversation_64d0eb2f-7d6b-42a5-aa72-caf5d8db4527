package lowlevel

import (
	"fmt"
	"unsafe"
)

// Basic PKCS#11 types mapped to Go types
type (
	// Basic types
	CKByte    uint8
	CKChar    uint8
	CKUTFChar uint8
	CKBool    uint8
	CKULong   uint64
	CKLong    int64
	CKFlags   uint64

	// Handle types
	CKSlotID        uint64
	CKSessionHandle uint64
	CKObjectHandle  uint64

	// Mechanism types
	CKMechanismType uint64
	CKAttributeType uint64
	CKObjectClass   uint64
	CKKeyType       uint64
	CKUserType      uint64
	CKState         uint64
	CKRV            uint64

	// Notification types
	CKNotification uint64
)

// Constants
const (
	CKTrue  = 1
	CKFalse = 0

	CKInvalidHandle = 0

	// Session flags
	CKFSerialSession = 0x00000004
	CKFRWSession     = 0x00000002

	// User types
	CKUSo      = 0
	CKUUser    = 1
	CKUContext = 2
)

// CKVersion represents PKCS#11 version information
type CKVersion struct {
	Major CKByte
	Minor CKByte
}

// String returns string representation of version
func (v *CKVersion) String() string {
	return fmt.Sprintf("%d.%d", v.Major, v.Minor)
}

// CKInfo represents PKCS#11 library information
type CKInfo struct {
	CryptokiVersion    CKVersion
	ManufacturerID     [32]CKUTFChar
	Flags              CKFlags
	LibraryDescription [32]CKUTFChar
	LibraryVersion     CKVersion
}

// CKSlotInfo represents slot information
type CKSlotInfo struct {
	SlotDescription [64]CKUTFChar
	ManufacturerID  [32]CKUTFChar
	Flags           CKFlags
	HardwareVersion CKVersion
	FirmwareVersion CKVersion
}

// CKTokenInfo represents token information
type CKTokenInfo struct {
	Label              [32]CKUTFChar
	ManufacturerID     [32]CKUTFChar
	Model              [16]CKUTFChar
	SerialNumber       [16]CKChar
	Flags              CKFlags
	MaxSessionCount    CKULong
	SessionCount       CKULong
	MaxRwSessionCount  CKULong
	RwSessionCount     CKULong
	MaxPinLen          CKULong
	MinPinLen          CKULong
	TotalPublicMemory  CKULong
	FreePublicMemory   CKULong
	TotalPrivateMemory CKULong
	FreePrivateMemory  CKULong
	HardwareVersion    CKVersion
	FirmwareVersion    CKVersion
	UTCTime            [16]CKChar
}

// CKMechanism represents a cryptographic mechanism
type CKMechanism struct {
	Mechanism CKMechanismType
	Parameter unsafe.Pointer
	ParameterLen CKULong
}

// CKAttribute represents an object attribute
type CKAttribute struct {
	Type     CKAttributeType
	Value    unsafe.Pointer
	ValueLen CKULong
}

// Helper functions for byte array conversions
func BytesToCBytes(data []byte) (*CKByte, CKULong) {
	if len(data) == 0 {
		return nil, 0
	}
	return (*CKByte)(unsafe.Pointer(&data[0])), CKULong(len(data))
}

func CBytesToBytes(ptr *CKByte, length CKULong) []byte {
	if ptr == nil || length == 0 {
		return nil
	}
	return (*[1 << 30]byte)(unsafe.Pointer(ptr))[:length:length]
}

// Helper functions for attribute conversions
func AttributesToCAttributes(attrs []CKAttribute) (*CKAttribute, CKULong) {
	if len(attrs) == 0 {
		return nil, 0
	}
	return &attrs[0], CKULong(len(attrs))
}

func CAttributesToAttributes(ptr *CKAttribute, count CKULong) []CKAttribute {
	if ptr == nil || count == 0 {
		return nil
	}
	return (*[1 << 20]CKAttribute)(unsafe.Pointer(ptr))[:count:count]
}

// String conversion helpers
func CKUTFCharArrayToString(arr []CKUTFChar) string {
	// Find the end of the string (first null byte or end of array)
	end := len(arr)
	for i, b := range arr {
		if b == 0 {
			end = i
			break
		}
	}
	
	// Convert to byte slice and then to string
	bytes := make([]byte, end)
	for i := 0; i < end; i++ {
		bytes[i] = byte(arr[i])
	}
	
	return string(bytes)
}

func StringToCKUTFCharArray(s string, size int) []CKUTFChar {
	arr := make([]CKUTFChar, size)
	
	// Copy string bytes to array
	for i, b := range []byte(s) {
		if i >= size {
			break
		}
		arr[i] = CKUTFChar(b)
	}
	
	// Pad with spaces (PKCS#11 convention)
	for i := len(s); i < size; i++ {
		arr[i] = CKUTFChar(' ')
	}
	
	return arr
}

// CKInfo helper methods
func (info *CKInfo) GetManufacturerID() string {
	return CKUTFCharArrayToString(info.ManufacturerID[:])
}

func (info *CKInfo) GetLibraryDescription() string {
	return CKUTFCharArrayToString(info.LibraryDescription[:])
}

func (info *CKInfo) SetManufacturerID(s string) {
	copy(info.ManufacturerID[:], StringToCKUTFCharArray(s, 32))
}

func (info *CKInfo) SetLibraryDescription(s string) {
	copy(info.LibraryDescription[:], StringToCKUTFCharArray(s, 32))
}

// CKSlotInfo helper methods
func (info *CKSlotInfo) GetSlotDescription() string {
	return CKUTFCharArrayToString(info.SlotDescription[:])
}

func (info *CKSlotInfo) GetManufacturerID() string {
	return CKUTFCharArrayToString(info.ManufacturerID[:])
}

func (info *CKSlotInfo) SetSlotDescription(s string) {
	copy(info.SlotDescription[:], StringToCKUTFCharArray(s, 64))
}

func (info *CKSlotInfo) SetManufacturerID(s string) {
	copy(info.ManufacturerID[:], StringToCKUTFCharArray(s, 32))
}

// CKTokenInfo helper methods
func (info *CKTokenInfo) GetLabel() string {
	return CKUTFCharArrayToString(info.Label[:])
}

func (info *CKTokenInfo) GetManufacturerID() string {
	return CKUTFCharArrayToString(info.ManufacturerID[:])
}

func (info *CKTokenInfo) GetModel() string {
	return CKUTFCharArrayToString(info.Model[:])
}

func (info *CKTokenInfo) GetSerialNumber() string {
	// Convert CKChar array to string (similar to CKUTFChar)
	end := len(info.SerialNumber)
	for i, b := range info.SerialNumber {
		if b == 0 {
			end = i
			break
		}
	}
	
	bytes := make([]byte, end)
	for i := 0; i < end; i++ {
		bytes[i] = byte(info.SerialNumber[i])
	}
	
	return string(bytes)
}

func (info *CKTokenInfo) SetLabel(s string) {
	copy(info.Label[:], StringToCKUTFCharArray(s, 32))
}

func (info *CKTokenInfo) SetManufacturerID(s string) {
	copy(info.ManufacturerID[:], StringToCKUTFCharArray(s, 32))
}

func (info *CKTokenInfo) SetModel(s string) {
	copy(info.Model[:], StringToCKUTFCharArray(s, 16))
}

// CKAttribute helper methods
func NewCKAttribute(attrType CKAttributeType, value interface{}) CKAttribute {
	attr := CKAttribute{Type: attrType}
	
	switch v := value.(type) {
	case []byte:
		if len(v) > 0 {
			attr.Value = unsafe.Pointer(&v[0])
			attr.ValueLen = CKULong(len(v))
		}
	case string:
		bytes := []byte(v)
		if len(bytes) > 0 {
			attr.Value = unsafe.Pointer(&bytes[0])
			attr.ValueLen = CKULong(len(bytes))
		}
	case CKULong:
		attr.Value = unsafe.Pointer(&v)
		attr.ValueLen = CKULong(unsafe.Sizeof(v))
	case CKBool:
		attr.Value = unsafe.Pointer(&v)
		attr.ValueLen = CKULong(unsafe.Sizeof(v))
	case CKObjectClass:
		attr.Value = unsafe.Pointer(&v)
		attr.ValueLen = CKULong(unsafe.Sizeof(v))
	case CKKeyType:
		attr.Value = unsafe.Pointer(&v)
		attr.ValueLen = CKULong(unsafe.Sizeof(v))
	default:
		// For other types, assume it's a pointer to the value
		attr.Value = unsafe.Pointer(&v)
		attr.ValueLen = CKULong(unsafe.Sizeof(v))
	}
	
	return attr
}

func (attr *CKAttribute) GetBytes() []byte {
	if attr.Value == nil || attr.ValueLen == 0 {
		return nil
	}
	return (*[1 << 30]byte)(attr.Value)[:attr.ValueLen:attr.ValueLen]
}

func (attr *CKAttribute) GetString() string {
	bytes := attr.GetBytes()
	if bytes == nil {
		return ""
	}
	return string(bytes)
}

func (attr *CKAttribute) GetULong() CKULong {
	if attr.Value == nil || attr.ValueLen < CKULong(unsafe.Sizeof(CKULong(0))) {
		return 0
	}
	return *(*CKULong)(attr.Value)
}

func (attr *CKAttribute) GetBool() CKBool {
	if attr.Value == nil || attr.ValueLen < CKULong(unsafe.Sizeof(CKBool(0))) {
		return CKFalse
	}
	return *(*CKBool)(attr.Value)
}
