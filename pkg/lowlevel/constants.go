package lowlevel

// PKCS#11 Return Values
const (
	CKROk                         = 0x00000000
	CKRCancel                     = 0x00000001
	CKRHostMemory                 = 0x00000002
	CKRSlotIDInvalid              = 0x00000003
	CKRGeneralError               = 0x00000005
	CKRFunctionFailed             = 0x00000006
	CKRArgumentsBad               = 0x00000007
	CKRNoEvent                    = 0x00000008
	CKRNeedToCreateThreads        = 0x00000009
	CKRCantLock                   = 0x0000000A
	CKRAttributeReadOnly          = 0x00000010
	CKRAttributeSensitive         = 0x00000011
	CKRAttributeTypeInvalid       = 0x00000012
	CKRAttributeValueInvalid      = 0x00000013
	CKRDataInvalid                = 0x00000020
	CKRDataLenRange               = 0x00000021
	CKRDeviceError                = 0x00000030
	CKRDeviceMemory               = 0x00000031
	CKRDeviceRemoved              = 0x00000032
	CKREncryptedDataInvalid       = 0x00000040
	CKREncryptedDataLenRange      = 0x00000041
	CKRFunctionCanceled           = 0x00000050
	CKRFunctionNotParallel        = 0x00000051
	CKRFunctionNotSupported       = 0x00000054
	CKRKeyHandleInvalid           = 0x00000060
	CKRKeySizeRange               = 0x00000062
	CKRKeyTypeInconsistent        = 0x00000063
	CKRKeyNotNeeded               = 0x00000064
	CKRKeyChanged                 = 0x00000065
	CKRKeyNeeded                  = 0x00000066
	CKRKeyIndigestible            = 0x00000067
	CKRKeyFunctionNotPermitted    = 0x00000068
	CKRKeyNotWrappable            = 0x00000069
	CKRKeyUnextractable           = 0x0000006A
	CKRMechanismInvalid           = 0x00000070
	CKRMechanismParamInvalid      = 0x00000071
	CKRObjectHandleInvalid        = 0x00000082
	CKROperationActive            = 0x00000090
	CKROperationNotInitialized    = 0x00000091
	CKRPinIncorrect               = 0x000000A0
	CKRPinInvalid                 = 0x000000A1
	CKRPinLenRange                = 0x000000A2
	CKRPinExpired                 = 0x000000A3
	CKRPinLocked                  = 0x000000A4
	CKRSessionClosed              = 0x000000B0
	CKRSessionCount               = 0x000000B1
	CKRSessionHandleInvalid       = 0x000000B3
	CKRSessionParallelNotSupported = 0x000000B4
	CKRSessionReadOnly            = 0x000000B5
	CKRSessionExists              = 0x000000B6
	CKRSessionReadOnlyExists      = 0x000000B7
	CKRSessionReadWriteSOExists   = 0x000000B8
	CKRSignatureInvalid           = 0x000000C0
	CKRSignatureLenRange          = 0x000000C1
	CKRTemplateIncomplete         = 0x000000D0
	CKRTemplateInconsistent       = 0x000000D1
	CKRTokenNotPresent            = 0x000000E0
	CKRTokenNotRecognized         = 0x000000E1
	CKRTokenWriteProtected        = 0x000000E2
	CKRUnwrappingKeyHandleInvalid = 0x000000F0
	CKRUnwrappingKeySizeRange     = 0x000000F1
	CKRUnwrappingKeyTypeInconsistent = 0x000000F2
	CKRUserAlreadyLoggedIn        = 0x00000100
	CKRUserNotLoggedIn            = 0x00000101
	CKRUserPinNotInitialized      = 0x00000102
	CKRUserTypeInvalid            = 0x00000103
	CKRUserAnotherAlreadyLoggedIn = 0x00000104
	CKRUserTooManyTypes           = 0x00000105
	CKRWrappedKeyInvalid          = 0x00000110
	CKRWrappedKeyLenRange         = 0x00000111
	CKRWrappingKeyHandleInvalid   = 0x00000112
	CKRWrappingKeySizeRange       = 0x00000113
	CKRWrappingKeyTypeInconsistent = 0x00000114
	CKRRandomSeedNotSupported     = 0x00000120
	CKRRandomNoRNG                = 0x00000121
	CKRDomainParamsInvalid        = 0x00000130
	CKRBufferTooSmall             = 0x00000150
	CKRSavedStateInvalid          = 0x00000160
	CKRInformationSensitive       = 0x00000170
	CKRStateUnsaveable            = 0x00000180
	CKRCryptokiNotInitialized     = 0x00000190
	CKRCryptokiAlreadyInitialized = 0x00000191
	CKRMutexBad                   = 0x000001A0
	CKRMutexNotLocked             = 0x000001A1
	CKRNewPinMode                 = 0x000001B0
	CKRNextOTP                    = 0x000001B1
	CKRFunctionRejected           = 0x00000200
	CKRVendorDefined              = 0x80000000
)

// Object Classes
const (
	CKOData           = 0x00000000
	CKOCertificate    = 0x00000001
	CKOPublicKey      = 0x00000002
	CKOPrivateKey     = 0x00000003
	CKOSecretKey      = 0x00000004
	CKOHWFeature      = 0x00000005
	CKODomainParams   = 0x00000006
	CKOMechanism      = 0x00000007
	CKOOTPKey         = 0x00000008
	CKOVendorDefined  = 0x80000000
)

// Key Types
const (
	CKKRsa            = 0x00000000
	CKKDsa            = 0x00000001
	CKKDh             = 0x00000002
	CKKECDSA          = 0x00000003
	CKKEC             = 0x00000003
	CKKX942DH         = 0x00000004
	CKKKea            = 0x00000005
	CKKGenericSecret  = 0x00000010
	CKKRc2            = 0x00000011
	CKKRc4            = 0x00000012
	CKKDes            = 0x00000013
	CKKDes2           = 0x00000014
	CKKDes3           = 0x00000015
	CKKCast           = 0x00000016
	CKKCast3          = 0x00000017
	CKKCast5          = 0x00000018
	CKKCast128        = 0x00000018
	CKKRc5            = 0x00000019
	CKKIdea           = 0x0000001A
	CKKSkipjack       = 0x0000001B
	CKKBaton          = 0x0000001C
	CKKJuniper        = 0x0000001D
	CKKCdmf           = 0x0000001E
	CKKAes            = 0x0000001F
	CKKBlowfish       = 0x00000020
	CKKTwofish        = 0x00000021
	CKKSecurid        = 0x00000022
	CKKHotp           = 0x00000023
	CKKActi           = 0x00000024
	CKKCamellia       = 0x00000025
	CKKAria           = 0x00000026
	CKKVendorDefined  = 0x80000000
)

// Mechanism Types
const (
	CKMRsaPkcsKeyPairGen     = 0x00000000
	CKMRsaPkcs               = 0x00000001
	CKMRsa9796               = 0x00000002
	CKMRsaX509               = 0x00000003
	CKMRsaPkcsOaep           = 0x00000009
	CKMRsaX931KeyPairGen     = 0x0000000A
	CKMRsaX931               = 0x0000000B
	CKMRsaPkcsPss            = 0x0000000D
	CKMDsaKeyPairGen         = 0x00000010
	CKMDsa                   = 0x00000011
	CKMDsaSha1               = 0x00000012
	CKMDsaSha224             = 0x00000013
	CKMDsaSha256             = 0x00000014
	CKMDsaSha384             = 0x00000015
	CKMDsaSha512             = 0x00000016
	CKMEcKeyPairGen          = 0x00001040
	CKMEcdsa                 = 0x00001041
	CKMEcdsaSha1             = 0x00001042
	CKMEcdsaSha224           = 0x00001043
	CKMEcdsaSha256           = 0x00001044
	CKMEcdsaSha384           = 0x00001045
	CKMEcdsaSha512           = 0x00001046
	CKMEcdh1Derive           = 0x00001050
	CKMEcdh1CofactorDerive   = 0x00001051
	CKMEcmqvDerive           = 0x00001052
	CKMAesKeyGen             = 0x00001080
	CKMAesEcb                = 0x00001081
	CKMAesCbc                = 0x00001082
	CKMAesMac                = 0x00001083
	CKMAesMacGeneral         = 0x00001084
	CKMAesCbcPad             = 0x00001085
	CKMAesCtr                = 0x00001086
	CKMAesGcm                = 0x00001087
	CKMAesCcm                = 0x00001088
	CKMAesCts                = 0x00001089
	CKMAesCmac               = 0x0000108A
	CKMAesCmacGeneral        = 0x0000108B
	CKMSha1                  = 0x00000220
	CKMSha224                = 0x00000255
	CKMSha256                = 0x00000250
	CKMSha384                = 0x00000260
	CKMSha512                = 0x00000270
	CKMSha1Hmac              = 0x00000221
	CKMSha1HmacGeneral       = 0x00000222
	CKMSha224Hmac            = 0x00000256
	CKMSha224HmacGeneral     = 0x00000257
	CKMSha256Hmac            = 0x00000251
	CKMSha256HmacGeneral     = 0x00000252
	CKMSha384Hmac            = 0x00000261
	CKMSha384HmacGeneral     = 0x00000262
	CKMSha512Hmac            = 0x00000271
	CKMSha512HmacGeneral     = 0x00000272
	CKMVendorDefined         = 0x80000000
)

// Attribute Types
const (
	CKAClass                 = 0x00000000
	CKAToken                 = 0x00000001
	CKAPrivate               = 0x00000002
	CKALabel                 = 0x00000003
	CKAApplication           = 0x00000010
	CKAValue                 = 0x00000011
	CKAObjectID              = 0x00000012
	CKACertificateType       = 0x00000080
	CKAIssuer                = 0x00000081
	CKASerialNumber          = 0x00000082
	CKAACIssuer              = 0x00000083
	CKAOwner                 = 0x00000084
	CKAAttrTypes             = 0x00000085
	CKATrusted               = 0x00000086
	CKACertificateCategory   = 0x00000087
	CKAJavaMidpSecurityDomain = 0x00000088
	CKAUrl                   = 0x00000089
	CKAHashOfSubjectPublicKey = 0x0000008A
	CKAHashOfIssuerPublicKey = 0x0000008B
	CKACheckValue            = 0x00000090
	CKAKeyType               = 0x00000100
	CKASubject               = 0x00000101
	CKAID                    = 0x00000102
	CKASensitive             = 0x00000103
	CKAEncrypt               = 0x00000104
	CKADecrypt               = 0x00000105
	CKAWrap                  = 0x00000106
	CKAUnwrap                = 0x00000107
	CKASign                  = 0x00000108
	CKASignRecover           = 0x00000109
	CKAVerify                = 0x0000010A
	CKAVerifyRecover         = 0x0000010B
	CKADerive                = 0x0000010C
	CKAStartDate             = 0x00000110
	CKAEndDate               = 0x00000111
	CKAModulus               = 0x00000120
	CKAModulusBits           = 0x00000121
	CKAPublicExponent        = 0x00000122
	CKAPrivateExponent       = 0x00000123
	CKAPrime1                = 0x00000124
	CKAPrime2                = 0x00000125
	CKAExponent1             = 0x00000126
	CKAExponent2             = 0x00000127
	CKACoefficient           = 0x00000128
	CKAPrime                 = 0x00000130
	CKASubprime              = 0x00000131
	CKABase                  = 0x00000132
	CKAPrimeBits             = 0x00000133
	CKASubprimeBits          = 0x00000134
	CKAValueBits             = 0x00000160
	CKAValueLen              = 0x00000161
	CKAExtractable           = 0x00000162
	CKALocal                 = 0x00000163
	CKANeverExtractable      = 0x00000164
	CKAAlwaysSensitive       = 0x00000165
	CKAKeyGenMechanism       = 0x00000166
	CKAModifiable            = 0x00000170
	CKAEcdsaParams           = 0x00000180
	CKAEcParams              = 0x00000180
	CKAEcPoint               = 0x00000181
	CKASecondaryAuth         = 0x00000200
	CKAAuthPinFlags          = 0x00000201
	CKAAlwaysAuthenticate    = 0x00000202
	CKAWrapWithTrusted       = 0x00000210
	CKAWrapTemplate          = 0x00000211
	CKAUnwrapTemplate        = 0x00000212
	CKAVendorDefined         = 0x80000000
)

// Session States
const (
	CKSRoPublicSession  = 0
	CKSRoUserFunctions  = 1
	CKSRwPublicSession  = 2
	CKSRwUserFunctions  = 3
	CKSRwSoFunctions    = 4
)

// Token Flags
const (
	CKFRng                    = 0x00000001
	CKFWriteProtected         = 0x00000002
	CKFLoginRequired          = 0x00000004
	CKFUserPinInitialized     = 0x00000008
	CKFRestoreKeyNotNeeded    = 0x00000020
	CKFClockOnToken           = 0x00000040
	CKFProtectedAuthenticationPath = 0x00000100
	CKFDualCryptoOperations   = 0x00000200
	CKFTokenInitialized       = 0x00000400
	CKFSecondaryAuthentication = 0x00000800
	CKFUserPinCountLow        = 0x00010000
	CKFUserPinFinalTry        = 0x00020000
	CKFUserPinLocked          = 0x00040000
	CKFUserPinToBeChanged     = 0x00080000
	CKFSoPinCountLow          = 0x00100000
	CKFSoPinFinalTry          = 0x00200000
	CKFSoPinLocked            = 0x00400000
	CKFSoPinToBeChanged       = 0x00800000
)

// Slot Flags
const (
	CKFTokenPresent       = 0x00000001
	CKFRemovableDevice    = 0x00000002
	CKFHwSlot             = 0x00000004
)

// Certificate Types
const (
	CKCx509               = 0x00000000
	CKCx509AttrCert       = 0x00000001
	CKCWtls               = 0x00000002
	CKCVendorDefined      = 0x80000000
)

// Hardware Feature Types
const (
	CKHMonotonicCounter = 0x00000001
	CKHClock            = 0x00000002
	CKHUserInterface    = 0x00000003
	CKHVendorDefined    = 0x80000000
)

// Special Values
const (
	CKUnavailableInformation = ^CKULong(0) // ~0UL
	CKEffectivelyInfinite    = CKULong(0)  // 0UL
)
