package lowlevel

import (
	"fmt"
)

// PKCS11Error represents a PKCS#11 error with additional context
type PKCS11Error struct {
	Code        CKRV
	Description string
	Function    string
	Context     string
}

// Error implements the error interface
func (e *PKCS11Error) Error() string {
	if e.Context != "" {
		return fmt.Sprintf("PKCS#11 error in %s: %s (%s) - %s", 
			e.Function, e.Description, ErrorName(uint(e.Code)), e.Context)
	}
	return fmt.Sprintf("PKCS#11 error in %s: %s (%s)", 
		e.Function, e.Description, ErrorName(uint(e.Code)))
}

// Code returns the PKCS#11 error code
func (e *PKCS11Error) GetCode() CKRV {
	return e.Code
}

// IsRetryable returns true if the error might be resolved by retrying
func (e *PKCS11Error) IsRetryable() bool {
	switch e.Code {
	case CKRDeviceMemory, CKRSessionCount, CKROperationActive:
		return true
	default:
		return false
	}
}

// IsAuthenticationError returns true if the error is related to authentication
func (e *PKCS11Error) IsAuthenticationError() bool {
	switch e.Code {
	case CKRPinIncorrect, CKRPinInvalid, CKRPinLenRange, 
		 CKRPinExpired, CKRPinLocked, CKRUserNotLoggedIn,
		 CKRUserAlreadyLoggedIn, CKRUserPinNotInitialized:
		return true
	default:
		return false
	}
}

// NewPKCS11Error creates a new PKCS11Error
func NewPKCS11Error(code CKRV, function string, context ...string) *PKCS11Error {
	err := &PKCS11Error{
		Code:        code,
		Description: ErrorName(uint(code)),
		Function:    function,
	}
	if len(context) > 0 {
		err.Context = context[0]
	}
	return err
}

// CheckResult checks a PKCS#11 return value and returns an error if it's not CKR_OK
func CheckResult(rv CKRV, function string, context ...string) error {
	if rv == CKROk {
		return nil
	}
	return NewPKCS11Error(rv, function, context...)
}

// ErrorName returns the string name for a PKCS#11 error code
func ErrorName(code uint) string {
	switch CKRV(code) {
	case CKROk:
		return "CKR_OK"
	case CKRCancel:
		return "CKR_CANCEL"
	case CKRHostMemory:
		return "CKR_HOST_MEMORY"
	case CKRSlotIDInvalid:
		return "CKR_SLOT_ID_INVALID"
	case CKRGeneralError:
		return "CKR_GENERAL_ERROR"
	case CKRFunctionFailed:
		return "CKR_FUNCTION_FAILED"
	case CKRArgumentsBad:
		return "CKR_ARGUMENTS_BAD"
	case CKRNoEvent:
		return "CKR_NO_EVENT"
	case CKRNeedToCreateThreads:
		return "CKR_NEED_TO_CREATE_THREADS"
	case CKRCantLock:
		return "CKR_CANT_LOCK"
	case CKRAttributeReadOnly:
		return "CKR_ATTRIBUTE_READ_ONLY"
	case CKRAttributeSensitive:
		return "CKR_ATTRIBUTE_SENSITIVE"
	case CKRAttributeTypeInvalid:
		return "CKR_ATTRIBUTE_TYPE_INVALID"
	case CKRAttributeValueInvalid:
		return "CKR_ATTRIBUTE_VALUE_INVALID"
	case CKRDataInvalid:
		return "CKR_DATA_INVALID"
	case CKRDataLenRange:
		return "CKR_DATA_LEN_RANGE"
	case CKRDeviceError:
		return "CKR_DEVICE_ERROR"
	case CKRDeviceMemory:
		return "CKR_DEVICE_MEMORY"
	case CKRDeviceRemoved:
		return "CKR_DEVICE_REMOVED"
	case CKREncryptedDataInvalid:
		return "CKR_ENCRYPTED_DATA_INVALID"
	case CKREncryptedDataLenRange:
		return "CKR_ENCRYPTED_DATA_LEN_RANGE"
	case CKRFunctionCanceled:
		return "CKR_FUNCTION_CANCELED"
	case CKRFunctionNotParallel:
		return "CKR_FUNCTION_NOT_PARALLEL"
	case CKRFunctionNotSupported:
		return "CKR_FUNCTION_NOT_SUPPORTED"
	case CKRKeyHandleInvalid:
		return "CKR_KEY_HANDLE_INVALID"
	case CKRKeySizeRange:
		return "CKR_KEY_SIZE_RANGE"
	case CKRKeyTypeInconsistent:
		return "CKR_KEY_TYPE_INCONSISTENT"
	case CKRKeyNotNeeded:
		return "CKR_KEY_NOT_NEEDED"
	case CKRKeyChanged:
		return "CKR_KEY_CHANGED"
	case CKRKeyNeeded:
		return "CKR_KEY_NEEDED"
	case CKRKeyIndigestible:
		return "CKR_KEY_INDIGESTIBLE"
	case CKRKeyFunctionNotPermitted:
		return "CKR_KEY_FUNCTION_NOT_PERMITTED"
	case CKRKeyNotWrappable:
		return "CKR_KEY_NOT_WRAPPABLE"
	case CKRKeyUnextractable:
		return "CKR_KEY_UNEXTRACTABLE"
	case CKRMechanismInvalid:
		return "CKR_MECHANISM_INVALID"
	case CKRMechanismParamInvalid:
		return "CKR_MECHANISM_PARAM_INVALID"
	case CKRObjectHandleInvalid:
		return "CKR_OBJECT_HANDLE_INVALID"
	case CKROperationActive:
		return "CKR_OPERATION_ACTIVE"
	case CKROperationNotInitialized:
		return "CKR_OPERATION_NOT_INITIALIZED"
	case CKRPinIncorrect:
		return "CKR_PIN_INCORRECT"
	case CKRPinInvalid:
		return "CKR_PIN_INVALID"
	case CKRPinLenRange:
		return "CKR_PIN_LEN_RANGE"
	case CKRPinExpired:
		return "CKR_PIN_EXPIRED"
	case CKRPinLocked:
		return "CKR_PIN_LOCKED"
	case CKRSessionClosed:
		return "CKR_SESSION_CLOSED"
	case CKRSessionCount:
		return "CKR_SESSION_COUNT"
	case CKRSessionHandleInvalid:
		return "CKR_SESSION_HANDLE_INVALID"
	case CKRSessionParallelNotSupported:
		return "CKR_SESSION_PARALLEL_NOT_SUPPORTED"
	case CKRSessionReadOnly:
		return "CKR_SESSION_READ_ONLY"
	case CKRSessionExists:
		return "CKR_SESSION_EXISTS"
	case CKRSessionReadOnlyExists:
		return "CKR_SESSION_READ_ONLY_EXISTS"
	case CKRSessionReadWriteSOExists:
		return "CKR_SESSION_READ_WRITE_SO_EXISTS"
	case CKRSignatureInvalid:
		return "CKR_SIGNATURE_INVALID"
	case CKRSignatureLenRange:
		return "CKR_SIGNATURE_LEN_RANGE"
	case CKRTemplateIncomplete:
		return "CKR_TEMPLATE_INCOMPLETE"
	case CKRTemplateInconsistent:
		return "CKR_TEMPLATE_INCONSISTENT"
	case CKRTokenNotPresent:
		return "CKR_TOKEN_NOT_PRESENT"
	case CKRTokenNotRecognized:
		return "CKR_TOKEN_NOT_RECOGNIZED"
	case CKRTokenWriteProtected:
		return "CKR_TOKEN_WRITE_PROTECTED"
	case CKRUnwrappingKeyHandleInvalid:
		return "CKR_UNWRAPPING_KEY_HANDLE_INVALID"
	case CKRUnwrappingKeySizeRange:
		return "CKR_UNWRAPPING_KEY_SIZE_RANGE"
	case CKRUnwrappingKeyTypeInconsistent:
		return "CKR_UNWRAPPING_KEY_TYPE_INCONSISTENT"
	case CKRUserAlreadyLoggedIn:
		return "CKR_USER_ALREADY_LOGGED_IN"
	case CKRUserNotLoggedIn:
		return "CKR_USER_NOT_LOGGED_IN"
	case CKRUserPinNotInitialized:
		return "CKR_USER_PIN_NOT_INITIALIZED"
	case CKRUserTypeInvalid:
		return "CKR_USER_TYPE_INVALID"
	case CKRUserAnotherAlreadyLoggedIn:
		return "CKR_USER_ANOTHER_ALREADY_LOGGED_IN"
	case CKRUserTooManyTypes:
		return "CKR_USER_TOO_MANY_TYPES"
	case CKRWrappedKeyInvalid:
		return "CKR_WRAPPED_KEY_INVALID"
	case CKRWrappedKeyLenRange:
		return "CKR_WRAPPED_KEY_LEN_RANGE"
	case CKRWrappingKeyHandleInvalid:
		return "CKR_WRAPPING_KEY_HANDLE_INVALID"
	case CKRWrappingKeySizeRange:
		return "CKR_WRAPPING_KEY_SIZE_RANGE"
	case CKRWrappingKeyTypeInconsistent:
		return "CKR_WRAPPING_KEY_TYPE_INCONSISTENT"
	case CKRRandomSeedNotSupported:
		return "CKR_RANDOM_SEED_NOT_SUPPORTED"
	case CKRRandomNoRNG:
		return "CKR_RANDOM_NO_RNG"
	case CKRDomainParamsInvalid:
		return "CKR_DOMAIN_PARAMS_INVALID"
	case CKRBufferTooSmall:
		return "CKR_BUFFER_TOO_SMALL"
	case CKRSavedStateInvalid:
		return "CKR_SAVED_STATE_INVALID"
	case CKRInformationSensitive:
		return "CKR_INFORMATION_SENSITIVE"
	case CKRStateUnsaveable:
		return "CKR_STATE_UNSAVEABLE"
	case CKRCryptokiNotInitialized:
		return "CKR_CRYPTOKI_NOT_INITIALIZED"
	case CKRCryptokiAlreadyInitialized:
		return "CKR_CRYPTOKI_ALREADY_INITIALIZED"
	case CKRMutexBad:
		return "CKR_MUTEX_BAD"
	case CKRMutexNotLocked:
		return "CKR_MUTEX_NOT_LOCKED"
	case CKRNewPinMode:
		return "CKR_NEW_PIN_MODE"
	case CKRNextOTP:
		return "CKR_NEXT_OTP"
	case CKRFunctionRejected:
		return "CKR_FUNCTION_REJECTED"
	case CKRVendorDefined:
		return "CKR_VENDOR_DEFINED"
	default:
		if code >= uint(CKRVendorDefined) {
			return fmt.Sprintf("CKR_VENDOR_DEFINED+0x%X", code-uint(CKRVendorDefined))
		}
		return fmt.Sprintf("CKR_UNKNOWN(0x%08X)", code)
	}
}

// IsSuccess checks if the return value indicates success
func IsSuccess(rv CKRV) bool {
	return rv == CKROk
}

// IsError checks if the return value indicates an error
func IsError(rv CKRV) bool {
	return rv != CKROk
}

// IsFatalError checks if the error is fatal and requires reinitialization
func IsFatalError(rv CKRV) bool {
	switch rv {
	case CKRDeviceRemoved, CKRDeviceError, CKRGeneralError:
		return true
	default:
		return false
	}
}
