// Package lowlevel provides low-level CGO bindings for PKCS#11 C API
package lowlevel

/*
#cgo CFLAGS: -I../../include/pkcs11-v30 -I../../include/pkcs11-v24 -I../../include/utimaco
#cgo LDFLAGS: -ldl

#include <stdlib.h>
#include <string.h>
#include <dlfcn.h>

// Define required PKCS#11 macros
#define CK_PTR *
#define CK_DECLARE_FUNCTION(returnType, name) returnType name
#define CK_DECLARE_FUNCTION_POINTER(returnType, name) returnType (* name)
#define CK_CALLBACK_FUNCTION(returnType, name) returnType (* name)
#ifndef NULL_PTR
#define NULL_PTR 0
#endif

// Include PKCS#11 headers based on version
#ifdef PKCS11_V30
    #include "pkcs11-v30/pkcs11.h"
#else
    #include "pkcs11-v24/pkcs11.h"
#endif

// Global variables for dynamic library loading
static void *pkcs11_lib = NULL;
static CK_FUNCTION_LIST_PTR pkcs11_functions = NULL;

// Function to load PKCS#11 library dynamically
CK_RV load_pkcs11_library(const char* library_path) {
    if (pkcs11_lib != NULL) {
        return CKR_CRYPTOKI_ALREADY_INITIALIZED;
    }

    pkcs11_lib = dlopen(library_path, RTLD_LAZY);
    if (pkcs11_lib == NULL) {
        return CKR_LIBRARY_LOAD_FAILED;
    }

    // Get the C_GetFunctionList function
    CK_C_GetFunctionList get_function_list =
        (CK_C_GetFunctionList)dlsym(pkcs11_lib, "C_GetFunctionList");

    if (get_function_list == NULL) {
        dlclose(pkcs11_lib);
        pkcs11_lib = NULL;
        return CKR_FUNCTION_FAILED;
    }

    // Get the function list
    CK_RV rv = get_function_list(&pkcs11_functions);
    if (rv != CKR_OK) {
        dlclose(pkcs11_lib);
        pkcs11_lib = NULL;
        return rv;
    }

    return CKR_OK;
}

// Function to unload PKCS#11 library
CK_RV unload_pkcs11_library() {
    if (pkcs11_lib != NULL) {
        dlclose(pkcs11_lib);
        pkcs11_lib = NULL;
        pkcs11_functions = NULL;
    }
    return CKR_OK;
}

// Wrapper functions for PKCS#11 API calls
CK_RV go_C_Initialize(CK_VOID_PTR pInitArgs) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_Initialize == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_Initialize(pInitArgs);
}

CK_RV go_C_Finalize(CK_VOID_PTR pReserved) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_Finalize == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_Finalize(pReserved);
}

CK_RV go_C_GetInfo(CK_INFO_PTR pInfo) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_GetInfo == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_GetInfo(pInfo);
}

CK_RV go_C_GetSlotList(CK_BBOOL tokenPresent, CK_SLOT_ID_PTR pSlotList, CK_ULONG_PTR pulCount) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_GetSlotList == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_GetSlotList(tokenPresent, pSlotList, pulCount);
}

CK_RV go_C_GetSlotInfo(CK_SLOT_ID slotID, CK_SLOT_INFO_PTR pInfo) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_GetSlotInfo == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_GetSlotInfo(slotID, pInfo);
}

CK_RV go_C_GetTokenInfo(CK_SLOT_ID slotID, CK_TOKEN_INFO_PTR pInfo) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_GetTokenInfo == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_GetTokenInfo(slotID, pInfo);
}

CK_RV go_C_OpenSession(CK_SLOT_ID slotID, CK_FLAGS flags, CK_VOID_PTR pApplication,
                       CK_NOTIFY Notify, CK_SESSION_HANDLE_PTR phSession) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_OpenSession == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_OpenSession(slotID, flags, pApplication, Notify, phSession);
}

CK_RV go_C_CloseSession(CK_SESSION_HANDLE hSession) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_CloseSession == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_CloseSession(hSession);
}

CK_RV go_C_Login(CK_SESSION_HANDLE hSession, CK_USER_TYPE userType,
                 CK_UTF8CHAR_PTR pPin, CK_ULONG ulPinLen) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_Login == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_Login(hSession, userType, pPin, ulPinLen);
}

CK_RV go_C_Logout(CK_SESSION_HANDLE hSession) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_Logout == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_Logout(hSession);
}

// Key management functions
CK_RV go_C_GenerateKeyPair(CK_SESSION_HANDLE hSession, CK_MECHANISM_PTR pMechanism,
                           CK_ATTRIBUTE_PTR pPublicKeyTemplate, CK_ULONG ulPublicKeyAttributeCount,
                           CK_ATTRIBUTE_PTR pPrivateKeyTemplate, CK_ULONG ulPrivateKeyAttributeCount,
                           CK_OBJECT_HANDLE_PTR phPublicKey, CK_OBJECT_HANDLE_PTR phPrivateKey) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_GenerateKeyPair == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_GenerateKeyPair(hSession, pMechanism,
                                               pPublicKeyTemplate, ulPublicKeyAttributeCount,
                                               pPrivateKeyTemplate, ulPrivateKeyAttributeCount,
                                               phPublicKey, phPrivateKey);
}

CK_RV go_C_GenerateKey(CK_SESSION_HANDLE hSession, CK_MECHANISM_PTR pMechanism,
                       CK_ATTRIBUTE_PTR pTemplate, CK_ULONG ulCount,
                       CK_OBJECT_HANDLE_PTR phKey) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_GenerateKey == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_GenerateKey(hSession, pMechanism, pTemplate, ulCount, phKey);
}

// Cryptographic functions
CK_RV go_C_EncryptInit(CK_SESSION_HANDLE hSession, CK_MECHANISM_PTR pMechanism, CK_OBJECT_HANDLE hKey) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_EncryptInit == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_EncryptInit(hSession, pMechanism, hKey);
}

CK_RV go_C_Encrypt(CK_SESSION_HANDLE hSession, CK_BYTE_PTR pData, CK_ULONG ulDataLen,
                   CK_BYTE_PTR pEncryptedData, CK_ULONG_PTR pulEncryptedDataLen) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_Encrypt == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_Encrypt(hSession, pData, ulDataLen, pEncryptedData, pulEncryptedDataLen);
}

CK_RV go_C_DecryptInit(CK_SESSION_HANDLE hSession, CK_MECHANISM_PTR pMechanism, CK_OBJECT_HANDLE hKey) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_DecryptInit == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_DecryptInit(hSession, pMechanism, hKey);
}

CK_RV go_C_Decrypt(CK_SESSION_HANDLE hSession, CK_BYTE_PTR pEncryptedData, CK_ULONG ulEncryptedDataLen,
                   CK_BYTE_PTR pData, CK_ULONG_PTR pulDataLen) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_Decrypt == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_Decrypt(hSession, pEncryptedData, ulEncryptedDataLen, pData, pulDataLen);
}

CK_RV go_C_SignInit(CK_SESSION_HANDLE hSession, CK_MECHANISM_PTR pMechanism, CK_OBJECT_HANDLE hKey) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_SignInit == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_SignInit(hSession, pMechanism, hKey);
}

CK_RV go_C_Sign(CK_SESSION_HANDLE hSession, CK_BYTE_PTR pData, CK_ULONG ulDataLen,
                CK_BYTE_PTR pSignature, CK_ULONG_PTR pulSignatureLen) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_Sign == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_Sign(hSession, pData, ulDataLen, pSignature, pulSignatureLen);
}

CK_RV go_C_VerifyInit(CK_SESSION_HANDLE hSession, CK_MECHANISM_PTR pMechanism, CK_OBJECT_HANDLE hKey) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_VerifyInit == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_VerifyInit(hSession, pMechanism, hKey);
}

CK_RV go_C_Verify(CK_SESSION_HANDLE hSession, CK_BYTE_PTR pData, CK_ULONG ulDataLen,
                  CK_BYTE_PTR pSignature, CK_ULONG ulSignatureLen) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_Verify == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_Verify(hSession, pData, ulDataLen, pSignature, ulSignatureLen);
}

// Random number generation
CK_RV go_C_GenerateRandom(CK_SESSION_HANDLE hSession, CK_BYTE_PTR RandomData, CK_ULONG ulRandomLen) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_GenerateRandom == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_GenerateRandom(hSession, RandomData, ulRandomLen);
}

// Object management functions
CK_RV go_C_FindObjectsInit(CK_SESSION_HANDLE hSession, CK_ATTRIBUTE_PTR pTemplate, CK_ULONG ulCount) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_FindObjectsInit == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_FindObjectsInit(hSession, pTemplate, ulCount);
}

CK_RV go_C_FindObjects(CK_SESSION_HANDLE hSession, CK_OBJECT_HANDLE_PTR phObject,
                       CK_ULONG ulMaxObjectCount, CK_ULONG_PTR pulObjectCount) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_FindObjects == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_FindObjects(hSession, phObject, ulMaxObjectCount, pulObjectCount);
}

CK_RV go_C_FindObjectsFinal(CK_SESSION_HANDLE hSession) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_FindObjectsFinal == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_FindObjectsFinal(hSession);
}

CK_RV go_C_GetAttributeValue(CK_SESSION_HANDLE hSession, CK_OBJECT_HANDLE hObject,
                             CK_ATTRIBUTE_PTR pTemplate, CK_ULONG ulCount) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_GetAttributeValue == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_GetAttributeValue(hSession, hObject, pTemplate, ulCount);
}

CK_RV go_C_SetAttributeValue(CK_SESSION_HANDLE hSession, CK_OBJECT_HANDLE hObject,
                             CK_ATTRIBUTE_PTR pTemplate, CK_ULONG ulCount) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_SetAttributeValue == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_SetAttributeValue(hSession, hObject, pTemplate, ulCount);
}

CK_RV go_C_CreateObject(CK_SESSION_HANDLE hSession, CK_ATTRIBUTE_PTR pTemplate,
                        CK_ULONG ulCount, CK_OBJECT_HANDLE_PTR phObject) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_CreateObject == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_CreateObject(hSession, pTemplate, ulCount, phObject);
}

CK_RV go_C_DestroyObject(CK_SESSION_HANDLE hSession, CK_OBJECT_HANDLE hObject) {
    if (pkcs11_functions == NULL || pkcs11_functions->C_DestroyObject == NULL) {
        return CKR_FUNCTION_FAILED;
    }
    return pkcs11_functions->C_DestroyObject(hSession, hObject);
}
*/
import "C"
import (
	"fmt"
	"unsafe"
)

// LoadLibrary loads the PKCS#11 library from the specified path
func LoadLibrary(libraryPath string) error {
	cPath := C.CString(libraryPath)
	defer C.free(unsafe.Pointer(cPath))

	rv := C.load_pkcs11_library(cPath)
	if rv != C.CKR_OK {
		return fmt.Errorf("failed to load PKCS#11 library: %s (0x%08X)", ErrorName(uint(rv)), rv)
	}

	return nil
}

// UnloadLibrary unloads the PKCS#11 library
func UnloadLibrary() error {
	rv := C.unload_pkcs11_library()
	if rv != C.CKR_OK {
		return fmt.Errorf("failed to unload PKCS#11 library: %s (0x%08X)", ErrorName(uint(rv)), rv)
	}

	return nil
}

// IsLibraryLoaded checks if the PKCS#11 library is loaded
func IsLibraryLoaded() bool {
	return C.pkcs11_lib != nil
}
